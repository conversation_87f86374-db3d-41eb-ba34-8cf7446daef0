{"name": "vexis", "type": "module", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "npx convex dev --once && concurrently \"pnpm dev:convex\" \"pnpm dev:start\"", "dev:convex": "convex dev", "dev:start": "vinxi dev", "seed": "npx convex dev --once && npx convex import --table tasks sampleData.jsonl", "build": "vinxi build", "start": "vinxi start"}, "keywords": [], "author": "", "dependencies": {"@convex-dev/auth": "^0.0.86", "@convex-dev/react-query": "^0.0.0-alpha.8", "@tanstack/react-query": "^5.59.20", "@tanstack/react-router": "^1.82.1", "@tanstack/react-router-with-query": "^1.82.1", "@tanstack/start": "^1.82.1", "@types/node": "^22.15.21", "axios": "^1.9.0", "bowser": "^2.11.0", "convex": "^1.23.0", "cryptr": "^6.3.0", "dotenv": "^16.5.0", "fast-xml-parser": "^5.2.3", "moviedb-promise": "^4.0.7", "react": "^18.3.1", "react-dom": "^18.3.1", "vinxi": "^0.4.3"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "concurrently": "^9.1.2", "jose": "^6.0.11", "typescript": "^5.6.3"}}