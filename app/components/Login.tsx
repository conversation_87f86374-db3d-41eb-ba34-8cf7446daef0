import {useAuthActions} from "@convex-dev/auth/react";
import {useEffect, useRef, useState} from "react";
import usePlexUser from "../hooks/usePlexUser";
import PlexLoginButton from "./PlexLoginButton";

interface LoginProps {
    onLogin?: () => void;
}

export function Login({ onLogin }: LoginProps) {
    const { signIn } = useAuthActions();
    const [authToken, setAuthToken] = useState<string>();
    const { data: plexUser } = usePlexUser(authToken);
    const formRef = useRef<HTMLFormElement>(null);

    useEffect(() => {
        if (plexUser) {
            formRef.current?.requestSubmit();
        }
    }, [plexUser]);

    return (
        <form
            ref={formRef}
            onSubmit={(event) => {
                event.preventDefault();
                const formData = new FormData(event.currentTarget);
                void signIn("plex", formData);
                onLogin?.();
            }}
        >
            <input name="email" type="hidden" value={plexUser?.email ?? ""} required />
            <input name="password" type="hidden" value={plexUser?.authToken ?? ""} required />
            <input name="name" type="hidden" value={plexUser?.title ?? ""} required />
            <PlexLoginButton onAuthToken={setAuthToken} />
        </form>
    );
}