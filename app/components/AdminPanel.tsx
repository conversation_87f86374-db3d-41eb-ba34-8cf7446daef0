import { useState } from "react";
import {useConvexMutation, useConvexQuery} from "@convex-dev/react-query";
import { api } from "../../convex/_generated/api";

export function AdminPanel() {
  const [syncStatus, setSyncStatus] = useState<string>("");
  const [recStatus, setRecStatus] = useState<string>("");
  const currentUser = useConvexQuery(api.auth.currentUser);

  // Sync Plex library mutation
  const syncPlexMutation = useConvexMutation(api.admin.syncPlexLibrary);

  // Generate recommendations mutation
  const generateRecsMutation = useConvexMutation(api.admin.generateRecommendations);

  // Handle sync button click
  const handleSync = async () => {
    setSyncStatus("Syncing...");
    try {
      await syncPlexMutation();
      setSyncStatus("Plex library sync has been scheduled.");
    } catch (error) {
      console.error("Error syncing Plex library:", error);
      setSyncStatus("Error syncing Plex library");
    }
  };

  // Handle generate recommendations button click
  const handleGenerateRecs = async () => {
    if (!currentUser) {
      setRecStatus("No user ID available");
      return;
    }

    setRecStatus("Generating recommendations...");
    try {
      await generateRecsMutation();
      setRecStatus("Recommendation generation has been scheduled.");
    } catch (error) {
      console.error("Error generating recommendations:", error);
      setRecStatus("Error generating recommendations");
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-xl font-semibold mb-4">Admin Panel</h2>

      <div className="mb-4 p-3 bg-gray-100 rounded-lg">
        <h3 className="font-medium mb-2">Status</h3>
        <p className="text-sm text-gray-700">
          User ID: {currentUser ? currentUser._id : "No user created yet"}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="font-medium mb-2">Plex Library Sync</h3>
          <p className="text-sm text-gray-600 mb-3">
            Manually sync your Plex library to update the database with your latest watched shows.
          </p>
          <button
            onClick={handleSync}
            disabled={syncStatus === "Syncing..."}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            {syncStatus === "Syncing..." ? "Syncing..." : "Sync Plex Library"}
          </button>
          {syncStatus && (
            <p className="mt-2 text-sm text-gray-600">{syncStatus}</p>
          )}
        </div>

        <div>
          <h3 className="font-medium mb-2">Generate Recommendations</h3>
          <p className="text-sm text-gray-600 mb-3">
            Manually generate recommendations based on your rated shows.
          </p>
          <button
            onClick={handleGenerateRecs}
            disabled={recStatus === "Generating recommendations..." || !currentUser}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            {recStatus === "Generating recommendations..." ? "Generating..." : "Generate Recommendations"}
          </button>
          {recStatus && (
            <p className="mt-2 text-sm text-gray-600">{recStatus}</p>
          )}
          {!currentUser && (
            <p className="mt-2 text-sm text-red-500">
              No user available. Please login.
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
