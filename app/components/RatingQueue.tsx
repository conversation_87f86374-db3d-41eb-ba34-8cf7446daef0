import { useState, useEffect } from 'react';
import { convexQuery, useConvexMutation } from "@convex-dev/react-query";
import { useSuspenseQuery } from "@tanstack/react-query";
import { api } from "../../convex/_generated/api";
import { StarRating } from './StarRating';
import { Link } from '@tanstack/react-router';

export function RatingQueue() {
  const [currentShow, setCurrentShow] = useState<any>(null);
  const [rating, setRating] = useState(0);
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showsRated, setShowsRated] = useState(0);

  // Get the next show to rate
  const { data: nextShow, refetch: refetchNextShow } = useSuspenseQuery(
    convexQuery(api.queries.getNextShowToRate, {})
  );

  // Get count of unrated shows for progress
  const { data: unratedShows } = useSuspenseQuery(
    convexQuery(api.queries.getUnratedWatchedShows, {})
  );

  // Rate show mutation
  const rateShowMutation = useConvexMutation(api.mutations.rateShowForCurrentUser);

  useEffect(() => {
    if (nextShow) {
      setCurrentShow(nextShow);
      setRating(0);
      setNotes('');
    }
  }, [nextShow]);

  const handleSubmitRating = async () => {
    if (!currentShow || rating === 0) return;

    setIsSubmitting(true);
    try {
      await rateShowMutation({
        showId: currentShow._id,
        rating,
        notes: notes || undefined,
      });

      setShowsRated(prev => prev + 1);

      // Fetch next show
      await refetchNextShow();
    } catch (error) {
      console.error('Error rating show:', error);
      alert('Failed to submit rating. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = async () => {
    setRating(0);
    setNotes('');
    await refetchNextShow();
  };

  if (!currentShow) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            🎉 All caught up!
          </h2>
          <p className="text-gray-600 mb-6">
            You've rated all your watched shows. Great job!
          </p>
          <Link
            to="/"
            className="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  const totalUnrated = unratedShows?.length || 0;
  const progressPercentage = totalUnrated > 0 ? ((showsRated / (totalUnrated + showsRated)) * 100) : 0;

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Rating Queue</h1>
          <p className="text-gray-600">Rate your watched shows to get better recommendations</p>
        </div>
        <Link
          to="/"
          className="px-4 py-2 text-blue-500 hover:text-blue-600 hover:underline"
        >
          ← Back to Home
        </Link>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">Progress</span>
          <span className="text-sm text-gray-600">
            {showsRated} rated • {totalUnrated} remaining
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Show Card */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
        <div className="md:flex">
          {/* Poster */}
          <div className="md:w-1/3">
            {currentShow.posterUrl ? (
              <img
                src={currentShow.posterUrl}
                alt={currentShow.title}
                className="w-full h-64 md:h-full object-cover"
              />
            ) : (
              <div className="w-full h-64 md:h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500 text-lg">No Image</span>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="md:w-2/3 p-6">
            <div className="mb-4">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                {currentShow.title}
              </h2>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                {currentShow.year && <span>{currentShow.year}</span>}
                <span className="capitalize">{currentShow.type}</span>
                {currentShow.lastWatched && (
                  <span>
                    Last watched: {new Date(currentShow.lastWatched).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>

            {/* Summary */}
            {currentShow.summary && (
              <p className="text-gray-700 mb-4 leading-relaxed">
                {currentShow.summary}
              </p>
            )}

            {/* Genres */}
            {currentShow.genres && currentShow.genres.length > 0 && (
              <div className="mb-6">
                <p className="text-sm text-gray-500 mb-2">Genres:</p>
                <div className="flex flex-wrap gap-2">
                  {currentShow.genres.map((genre: string) => (
                    <span
                      key={genre}
                      className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                    >
                      {genre}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Rating Section */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                How would you rate this {currentShow.type}?
              </h3>

              <div className="mb-4">
                <StarRating
                  rating={rating}
                  onRatingChange={setRating}
                  size="lg"
                />
              </div>

              {/* Notes */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes (optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add any thoughts about this show..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  onClick={handleSubmitRating}
                  disabled={rating === 0 || isSubmitting}
                  className="flex-1 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Rating'}
                </button>
                <button
                  onClick={handleSkip}
                  disabled={isSubmitting}
                  className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  Skip
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
