import usePlexLogin from '../hooks/usePlexLogin';

interface PlexLoginButtonProps {
    onAuthToken: (authToken: string) => void;
    isProcessing?: boolean;
    onError?: (message: string) => void;
    large?: boolean;
}

const PlexLoginButton = ({
                             onAuthToken,
                             onError,
                             isProcessing,
                             large,
                         }: PlexLoginButtonProps) => {
    const { loading, login } = usePlexLogin({ onAuthToken, onError });

    return (
        <button
            type="button"
            className="relative flex-1 border-[#cc7b19] bg-[rgba(204,123,25,0.3)] hover:border-[#cc7b19] hover:bg-[rgba(204,123,25,0.7)] disabled:opacity-50"
            onClick={login}
            disabled={loading || isProcessing}
            data-testid="plex-login-button"
        >
            {loading && (
                <div className="absolute right-0 mr-4 h-4 w-4">
                    Loading...
                </div>
            )}

            Login with Plex
        </button>
    );
};

export default PlexLoginButton;