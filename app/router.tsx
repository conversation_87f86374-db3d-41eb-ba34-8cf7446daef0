import {createRouter as createTanStackRouter} from "@tanstack/react-router";
import {QueryClient} from "@tanstack/react-query";
import {routerWithQueryClient} from "@tanstack/react-router-with-query";
import {ConvexQueryClient} from "@convex-dev/react-query";
import {ConvexProvider} from "convex/react";
import {routeTree} from "./routeTree.gen";
import {ConvexAuthProvider} from "@convex-dev/auth/react";

export function createRouter() {
  const CONVEX_URL = (import.meta as any).env.VITE_CONVEX_URL!;
  if (!CONVEX_URL) {
    console.error("missing envar CONVEX_URL");
  }
  const convexQueryClient = new ConvexQueryClient(CONVEX_URL, { verbose: true });

  const queryClient: QueryClient = new QueryClient({
    defaultOptions: {
      queries: {
        queryKeyHashFn: convexQueryClient.hashFn(),
        queryFn: convexQueryClient.queryFn(),
        staleTime: 1000 * 60 * 5, // 5 minutes
        refetchOnWindowFocus: false,
      },
    },
  });
  convexQueryClient.connect(queryClient);

  return routerWithQueryClient(
      createTanStackRouter({
        routeTree,
        defaultPreload: "intent",
        scrollRestoration: true,
        context: {queryClient},
        Wrap: ({children}) => (
            <ConvexAuthProvider client={convexQueryClient.convexClient}>
              {children}
            </ConvexAuthProvider>
        ),
      }),
      queryClient,
  );
}

declare module "@tanstack/react-router" {
  interface Register {
    router: ReturnType<typeof createRouter>;
  }
}
