import { convexQuery } from "@convex-dev/react-query";
import { useSuspenseQuery } from "@tanstack/react-query";
import {createFileRoute, Link} from "@tanstack/react-router";
import { api } from "../../convex/_generated/api";
import { AdminPanel } from "../components/AdminPanel";

export const Route = createFileRoute("/")({
  component: Home,
});

function Home() {
  const { isLoading, data } = useSuspenseQuery(convexQuery(api.queries.getWatchedShows, {}))

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Plex Recommendations</h1>

      <AdminPanel />

      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-semibold">Recently Watched</h2>
          <Link to="/watched" className="text-blue-500 hover:underline">
            View All
          </Link>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <p>Loading recently watched shows...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {data.map((show) => (
            <div key={show._id} className="bg-gray-100 rounded-lg overflow-hidden shadow-md">
              {show.posterUrl ? (
                <img
                  src={show.posterUrl}
                  alt={show.title}
                  className="w-full h-48 object-cover"
                />
              ) : (
                <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-500">No Image</span>
                </div>
              )}
              <div className="p-3">
                <h3 className="font-medium text-sm truncate">{show.title}</h3>
                {show.year && <p className="text-xs text-gray-600">{show.year}</p>}
                {show.fullyWatched && (
                  <Link
                    to="/rate/$showId"
                    params={{ showId: show._id }}
                    className="mt-2 text-xs text-blue-500 block hover:underline"
                  >
                    Rate This
                  </Link>
                )}
              </div>
            </div>
          ))}
          </div>
        )}
      </div>

      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-semibold">Your Recommendations</h2>
          <Link to="/recommendations" className="text-blue-500 hover:underline">
            View All
          </Link>
        </div>
        <div className="bg-gray-100 p-6 rounded-lg text-center">
          <p className="text-gray-600">
            Rate shows you've watched to get personalized recommendations
          </p>
          <Link
            to="/"
            search={{ path: '/watched' }}
            className="mt-4 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Rate Shows
          </Link>
        </div>
      </div>
    </div>
  );
}
