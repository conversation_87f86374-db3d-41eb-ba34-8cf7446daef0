import { convexQuery } from "@convex-dev/react-query";
import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { api } from "../../convex/_generated/api";

export const Route = createFileRoute('/watched')({
  component: WatchedShows,
});

function WatchedShows() {
  const { isLoading, data } = useSuspenseQuery(convexQuery(api.queries.getWatchedShows, {}));

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link to="/" className="text-blue-500 hover:underline mr-4">
          &larr; Back to Home
        </Link>
        <h1 className="text-3xl font-bold">Watched Shows</h1>
      </div>

      <p className="mb-6 text-gray-600">
        These are shows you've completely watched. Rate them to get personalized recommendations.
      </p>

      {isLoading ? (
        <div className="text-center py-12">
          <p>Loading watched shows...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {data.map((show) => (
          <div key={show._id} className="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
            {show.posterUrl ? (
              <img
                src={show.posterUrl}
                alt={show.title}
                className="w-full h-64 object-cover"
              />
            ) : (
              <div className="w-full h-64 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">No Image</span>
              </div>
            )}
            <div className="p-4">
              <h3 className="font-semibold text-lg mb-1">{show.title}</h3>
              {show.year && <p className="text-sm text-gray-600 mb-2">{show.year}</p>}

              {show.summary && (
                <p className="text-sm text-gray-700 mb-4 line-clamp-3">
                  {show.summary}
                </p>
              )}

              <Link
                to="/"
                search={{ path: `/rate/${show._id}` }}
                className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 w-full text-center"
              >
                Rate This Show
              </Link>
            </div>
          </div>
        ))}

          {data.length === 0 && (
            <div className="col-span-full text-center py-12 bg-gray-100 rounded-lg">
              <p className="text-gray-600 mb-4">No fully watched shows found.</p>
              <p className="text-sm text-gray-500">
                Shows will appear here once you've completely watched them in Plex.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
