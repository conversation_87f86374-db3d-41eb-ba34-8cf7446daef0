import {createFileRoute, useRouter} from "@tanstack/react-router";
import {Login} from "../components/Login";


export const Route = createFileRoute("/login")({
    component: LoginPage,
    validateSearch: (search) => {
        return {
            redirect: search.redirect as (string | undefined),
        };
    },
});

function LoginPage() {
    const { redirect } = Route.useSearch();
    const router = useRouter();

    return (
        <Login onLogin={() => {
            if (redirect) {
                router.history.push(redirect);
            }
        }} />
    );
}