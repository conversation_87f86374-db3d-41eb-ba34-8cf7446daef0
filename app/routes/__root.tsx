import { QueryClient } from "@tanstack/react-query";
import { createRootRouteWithContext, Link } from "@tanstack/react-router";
import { Outlet } from "@tanstack/react-router";
import { <PERSON>a, Scripts } from "@tanstack/start";
import * as React from "react";
import {Authenticated, Unauthenticated} from "convex/react";
import {Login} from "../components/Login";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      {
        title: "Plex Recommendations",
      },
    ],
    links: [
      {
        rel: "stylesheet",
        href: "https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css",
      },
    ],
  }),
  notFoundComponent: () => (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
      <h1 className="text-4xl font-bold text-gray-800 mb-4">Page Not Found</h1>
      <p className="text-gray-600 mb-6">The page you're looking for doesn't exist.</p>
      <Link to="/" className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
        Go Home
      </Link>
    </div>
  ),
  component: RootComponent,
});

function RootComponent() {


  return (
    <RootDocument>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-blue-600 text-white shadow-md">
          <div className="container mx-auto p-4">
            <nav className="flex justify-between items-center">
              <Link to="/" className="text-xl font-bold">Plex Recommendations</Link>
              <Authenticated>
                <div className="space-x-4">
                  <Link to="/watched" className="hover:underline">Watched Shows</Link>
                  <Link to="/recommendations" className="hover:underline">Recommendations</Link>
                </div>
              </Authenticated>
              <Unauthenticated>
                <Link to="/login" search={{ redirect: undefined }} className="hover:underline">Login</Link>
              </Unauthenticated>
            </nav>
          </div>
        </header>
        <main className="py-6">
          <Authenticated>
            <Outlet />
          </Authenticated>
          <Unauthenticated>
            <Login />
          </Unauthenticated>
        </main>
        <footer className="bg-gray-800 text-white py-6">
          <div className="container mx-auto px-4 text-center">
            <p>&copy; {new Date().getFullYear()} Plex Recommendations</p>
            <p className="text-sm text-gray-400 mt-2">
              Powered by Plex, TMDB, and Convex
            </p>
          </div>
        </footer>
      </div>
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <Meta />
      </head>
      <body>
        {children}
        <Scripts />
      </body>
    </html>
  );
}
