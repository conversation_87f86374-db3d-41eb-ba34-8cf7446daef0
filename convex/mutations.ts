import { mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

// Rate a show
export const rateShow = mutation({
  args: {
    userId: v.id("users"),
    showId: v.id("plexShows"),
    rating: v.number(),
    notes: v.optional(v.string()),
  },
  returns: v.id("ratings"),
  handler: async (ctx, args) => {
    // Check if user has already rated this show
    const existingRating = await ctx.db
      .query("ratings")
      .withIndex("by_userId_and_showId", (q) =>
        q.eq("userId", args.userId).eq("showId", args.showId)
      )
      .unique();

    if (existingRating) {
      // Update existing rating
      await ctx.db.patch(existingRating._id, {
        rating: args.rating,
        notes: args.notes,
      });
      return existingRating._id;
    } else {
      // Create new rating
      const ratingId = await ctx.db.insert("ratings", {
        userId: args.userId,
        showId: args.showId,
        rating: args.rating,
        notes: args.notes,
        createdAt: Date.now(),
      });

      // Schedule recommendation generation
      await ctx.scheduler.runAfter(0, internal.tmdb.generateRecommendations, {
        userId: args.userId,
      });

      return ratingId;
    }
  },
});

// Store a recommendation
export const storeRecommendation = internalMutation({
  args: {
    userId: v.id("users"),
    showTitle: v.string(),
    tmdbId: v.string(),
    posterUrl: v.optional(v.string()),
    summary: v.optional(v.string()),
    similarToShowId: v.id("plexShows"),
    confidence: v.number(),
  },
  returns: v.id("recommendations"),
  handler: async (ctx, args) => {
    // Check if this recommendation already exists
    const existingRec = await ctx.db
      .query("recommendations")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("tmdbId"), args.tmdbId))
      .unique();

    if (existingRec) {
      // Update confidence if the new one is higher
      if (args.confidence > existingRec.confidence) {
        await ctx.db.patch(existingRec._id, {
          confidence: args.confidence,
          similarToShowId: args.similarToShowId,
        });
      }
      return existingRec._id;
    } else {
      // Create new recommendation
      return await ctx.db.insert("recommendations", {
        userId: args.userId,
        showTitle: args.showTitle,
        tmdbId: args.tmdbId,
        posterUrl: args.posterUrl,
        summary: args.summary,
        similarToShowId: args.similarToShowId,
        confidence: args.confidence,
        createdAt: Date.now(),
        viewed: false,
      });
    }
  },
});

// Mark a recommendation as viewed
export const markRecommendationViewed = mutation({
  args: {
    recommendationId: v.id("recommendations"),
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.recommendationId, {
      viewed: true,
    });
    return true;
  },
});
