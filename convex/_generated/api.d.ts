/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as CustomProfile from "../CustomProfile.js";
import type * as admin from "../admin.js";
import type * as auth from "../auth.js";
import type * as crons_index from "../crons/index.js";
import type * as crons from "../crons.js";
import type * as crypt from "../crypt.js";
import type * as http from "../http.js";
import type * as mutations from "../mutations.js";
import type * as plex from "../plex.js";
import type * as queries from "../queries.js";
import type * as tmdb from "../tmdb.js";
import type * as user from "../user.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  CustomProfile: typeof CustomProfile;
  admin: typeof admin;
  auth: typeof auth;
  "crons/index": typeof crons_index;
  crons: typeof crons;
  crypt: typeof crypt;
  http: typeof http;
  mutations: typeof mutations;
  plex: typeof plex;
  queries: typeof queries;
  tmdb: typeof tmdb;
  user: typeof user;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
