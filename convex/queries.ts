import { query, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {api, internal} from "./_generated/api";

// Get all shows from the database
export const getAllShows = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      genres: v.optional(v.array(v.string())),
      tmdbId: v.optional(v.string()),
      imdbId: v.optional(v.string()),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
    })
  ),
  handler: async (ctx) => {
    return await ctx.db.query("plexShows").collect();
  },
});

// Get fully watched shows that can be rated
export const getWatchedShows = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      genres: v.optional(v.array(v.string())),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
      partiallyWatched: v.boolean(),
      type: v.string(),
      _creationTime: v.number(),
    })
  ),
  handler: async (ctx) => {
      const shows = await ctx.db
          .query("plexShows")
          .withIndex("by_fullyWatched", (q) => q.eq("fullyWatched", true))
          .collect();

      const plexServerUrl = process.env.PLEX_SERVER_URL;

      const token = await ctx.runQuery(internal.user.getPlexToken, {});

      shows.sort((a, b) => (b.lastWatched ?? 0) - (a.lastWatched ?? 0));

      shows.forEach(show => {
          show.posterUrl = show.posterUrl ? `${plexServerUrl}/library/metadata/${show.plexId}/thumb/${show.posterUrl}?X-Plex-Token=${token}` : undefined;
      });

    return shows;
  },
});

// Get recently watched shows
export const getRecentlyWatchedShows = query({
  args: {
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
      _creationTime: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    return await ctx.db
      .query("plexShows")
      .withIndex("by_lastWatched")
      .order("desc")
      .filter((q) => q.neq(q.field("lastWatched"), null))
      .take(limit);
  },
});

// Get a show by ID
export const getShowById = internalQuery({
  args: {
    showId: v.id("plexShows"),
  },
  returns: v.union(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      genres: v.optional(v.array(v.string())),
      tmdbId: v.optional(v.string()),
      imdbId: v.optional(v.string()),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
      partiallyWatched: v.boolean(),
      type: v.string(),
      _creationTime: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    return await ctx.db.get(args.showId);
  },
});

// Get a show by Plex ID
export const getShowByPlexId = internalQuery({
  args: {
    plexId: v.number(),
  },
  returns: v.union(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      genres: v.optional(v.array(v.string())),
      tmdbId: v.optional(v.string()),
      imdbId: v.optional(v.string()),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
      partiallyWatched: v.boolean(),
      type: v.string(),
      _creationTime: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("plexShows")
      .withIndex("by_plexId", (q) => q.eq("plexId", args.plexId))
      .unique();
  },
});

// Get user's ratings
export const getUserRatings = query({
  args: {
    userId: v.id("users"),
  },
  returns: v.array(
    v.object({
      _id: v.id("ratings"),
      userId: v.id("users"),
      showId: v.id("plexShows"),
      rating: v.number(),
      createdAt: v.number(),
      notes: v.optional(v.string()),
      show: v.union(
        v.object({
          _id: v.id("plexShows"),
          title: v.string(),
          posterUrl: v.optional(v.string()),
        }),
        v.object({
          _id: v.string(),
          title: v.string(),
          posterUrl: v.optional(v.string()),
        })
      ),
    })
  ),
  handler: async (ctx, args) => {
    const ratings = await ctx.db
      .query("ratings")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .collect();

    // Fetch show details for each rating
    const ratingsWithShows = await Promise.all(
      ratings.map(async (rating) => {
        const show = await ctx.db.get(rating.showId);
        return {
          ...rating,
          show: show ? {
            _id: show._id,
            title: show.title,
            posterUrl: show.posterUrl,
          } : { _id: "", title: "Unknown Show", posterUrl: undefined },
        };
      })
    );

    return ratingsWithShows;
  },
});

// Get highly rated shows for a user (4-5 stars)
export const getHighlyRatedShows = internalQuery({
  args: {
    userId: v.id("users"),
  },
  returns: v.array(
    v.object({
      _id: v.id("ratings"),
      userId: v.id("users"),
      showId: v.id("plexShows"),
      rating: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("ratings")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .filter((q) => q.gte(q.field("rating"), 4))
      .collect();
  },
});

// Get watched shows that haven't been rated by the current user
export const getUnratedWatchedShows = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      genres: v.optional(v.array(v.string())),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
      partiallyWatched: v.boolean(),
      type: v.string(),
      _creationTime: v.number(),
    })
  ),
  handler: async (ctx) => {
    // Get current user
    const currentUser = await ctx.runQuery(api.auth.currentUser, {});
    if (!currentUser) {
      throw new Error("User not authenticated");
    }

    // Get all watched shows
    const watchedShows = await ctx.db
      .query("plexShows")
      .withIndex("by_fullyWatched", (q) => q.eq("fullyWatched", true))
      .collect();

    // Get all ratings by this user
    const userRatings = await ctx.db
      .query("ratings")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .collect();

    // Create a set of rated show IDs for quick lookup
    const ratedShowIds = new Set(userRatings.map(rating => rating.showId));

    // Filter out shows that have already been rated
    const unratedShows = watchedShows.filter(show => !ratedShowIds.has(show._id));

    // Add poster URLs with Plex token
    const plexServerUrl = process.env.PLEX_SERVER_URL;
    const token = await ctx.runQuery(internal.user.getPlexToken, {});

    unratedShows.forEach(show => {
      show.posterUrl = show.posterUrl ? `${plexServerUrl}/library/metadata/${show.plexId}/thumb/${show.posterUrl}?X-Plex-Token=${token}` : undefined;
    });

    // Sort by last watched (most recent first)
    unratedShows.sort((a, b) => (b.lastWatched ?? 0) - (a.lastWatched ?? 0));

    return unratedShows;
  },
});

// Get a single random unrated show for the queue
export const getNextShowToRate = query({
  args: {},
  returns: v.union(
    v.object({
      _id: v.id("plexShows"),
      title: v.string(),
      year: v.optional(v.number()),
      plexId: v.number(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      genres: v.optional(v.array(v.string())),
      lastWatched: v.optional(v.number()),
      fullyWatched: v.boolean(),
      partiallyWatched: v.boolean(),
      type: v.string(),
      _creationTime: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx) => {
    // Get all unrated shows
    const unratedShows = await ctx.runQuery(api.queries.getUnratedWatchedShows, {});

    if (unratedShows.length === 0) {
      return null;
    }

    // Return a random show from the unrated list
    const randomIndex = Math.floor(Math.random() * unratedShows.length);
    return unratedShows[randomIndex];
  },
});

// Get recommendations for a user
export const getUserRecommendations = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("recommendations"),
      userId: v.id("users"),
      showTitle: v.string(),
      tmdbId: v.string(),
      posterUrl: v.optional(v.string()),
      summary: v.optional(v.string()),
      similarToShowId: v.id("plexShows"),
      confidence: v.number(),
      createdAt: v.number(),
      viewed: v.boolean(),
      similarToShow: v.object({
        title: v.string(),
      }),
    })
  ),
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    const recommendations = await ctx.db
      .query("recommendations")
      .withIndex("by_userId_and_viewed", (q) =>
        q.eq("userId", args.userId).eq("viewed", false)
      )
      .order("desc")
      .take(limit);

    // Fetch the show that each recommendation is similar to
    const recommendationsWithShows = await Promise.all(
      recommendations.map(async (rec) => {
        const similarShow = await ctx.db.get(rec.similarToShowId);
        return {
          ...rec,
          similarToShow: similarShow ? {
            title: similarShow.title,
          } : {
            title: "Unknown Show",
          },
        };
      })
    );

    return recommendationsWithShows;
  },
});
