import {PasswordConfig} from "@convex-dev/auth/providers/Password";
import { Scrypt } from "lucia";
import {GenericDataModel} from "convex/server";
import {ConvexCredentials} from "@convex-dev/auth/providers/ConvexCredentials";
import {
    createAccount,
    GenericDoc,
    retrieveAccount,
} from "@convex-dev/auth/server";
import {decrypt, encrypt} from "./crypt";


function Plex<DataModel extends GenericDataModel>(
    config: PasswordConfig<DataModel> = {},
) {
    const provider = config.id ?? "plex";
    return ConvexCredentials<DataModel>({
        id: "plex",
        authorize: async (params, ctx) => {
            const encrypted = await encrypt(params.password as string);
            if (!encrypted) {
                throw new Error("Failed to decrypt token");
            }
            const secret = params.password as string;
            params.password = encrypted;
            const profile = config.profile!(params, ctx);
            const { email } = profile;
            let account: GenericDoc<DataModel, "authAccounts">;
            let user: GenericDoc<DataModel, "users">;

            let retrieved: { account: GenericDoc<DataModel, "authAccounts">, user: GenericDoc<DataModel, "users"> } | null = null;

            try {
                retrieved = await retrieveAccount(ctx, {
                    provider,
                    account: {id: email, secret},
                });
            } catch (err) {
                console.error(err);
            }
            if (retrieved === null) {
                try {
                    retrieved = await createAccount(ctx, {
                        provider,
                        account: {id: email, secret},
                        profile: profile as any,
                        shouldLinkViaEmail: config.verify !== undefined,
                        shouldLinkViaPhone: false,
                    });
                } catch (err) {
                    throw new Error("Failed to create account: " + err);
                }
            }
            ({ account, user } = retrieved)
            return { userId: user._id };
        },
        crypto: {
            async hashSecret(password: string) {
                return await new Scrypt().hash(password);
            },
            async verifySecret(password: string, hash: string) {
                return await new Scrypt().verify(hash, password);
            },
        },
        ...config,
    });
}

export default Plex({
    profile: (params) => {
        return {
            email: params.email as string,
            name: params.name as string,
            token: params.password as string,
        };
    },
})