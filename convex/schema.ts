import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import {authTables} from "@convex-dev/auth/server";

export default defineSchema({
  ...authTables,
  // Users table to store user information
  users: defineTable({
    name: v.string(),
    email: v.string(),
    token: v.string(),
  }).index("by_email", ["email"]),

  // Plex shows table to store information about shows from Plex library
  plexShows: defineTable({
    title: v.string(),
    year: v.optional(v.number()),
    plexId: v.number(), // Unique identifier from Plex
    posterUrl: v.optional(v.string()),
    summary: v.optional(v.string()),
    genres: v.optional(v.array(v.string())),
    tmdbId: v.optional(v.string()), // TMDB ID for fetching recommendations
    imdbId: v.optional(v.string()), // IMDB ID as alternative identifier
    type: v.string(), // "show" / "movie" / "anime
    lastWatched: v.optional(v.number()), // Timestamp of last watch
    fullyWatched: v.boolean(), // Whether the show has been fully watched
    partiallyWatched: v.boolean(), // Whether the show has been partially watched
  }).index("by_plexId", ["plexId"])
    .index("by_fullyWatched", ["fullyWatched"])
    .index("by_lastWatched", ["lastWatched"]),

  // Ratings table to store user ratings for shows
  ratings: defineTable({
    userId: v.id("users"),
    showId: v.id("plexShows"),
    rating: v.number(), // Rating from 1-5
    createdAt: v.number(),
    notes: v.optional(v.string()),
  }).index("by_userId", ["userId"])
    .index("by_showId", ["showId"])
    .index("by_userId_and_showId", ["userId", "showId"]),

  // Recommendations table to store recommendations for users
  recommendations: defineTable({
    userId: v.id("users"),
    showTitle: v.string(),
    tmdbId: v.string(),
    posterUrl: v.optional(v.string()),
    summary: v.optional(v.string()),
    similarToShowId: v.id("plexShows"), // Which show this is similar to
    confidence: v.number(), // How confident we are in this recommendation (0-1)
    createdAt: v.number(),
    viewed: v.boolean(), // Whether the user has viewed this recommendation
  }).index("by_userId", ["userId"])
    .index("by_userId_and_viewed", ["userId", "viewed"])
    .index("by_tmdbId", ["tmdbId"]),
});
