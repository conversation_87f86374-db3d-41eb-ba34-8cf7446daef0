
const ALGORITHM = 'AES-GCM';
const IV_LENGTH = 12; // Recommended for AES-GCM
const KEY_DIGEST_ALGORITHM = 'SHA-256';

/**
 * Encodes text to a Uint8Array.
 * @type {TextEncoder}
 */
const encoder: TextEncoder = new TextEncoder();

/**
 * Decodes a Uint8Array to text.
 * @type {TextDecoder}
 */
const decoder: TextDecoder = new TextDecoder();

/**
 * Converts an ArrayBuffer to a Base64 string.
 * @param buffer The ArrayBuffer to convert.
 * @returns The Base64 encoded string.
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

/**
 * Converts a Base64 string to a Uint8Array.
 * @param base64 The Base64 string to convert.
 * @returns The decoded Uint8Array.
 */
function base64ToUint8Array(base64: string): Uint8Array {
    const binary_string = atob(base64);
    const len = binary_string.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binary_string.charCodeAt(i);
    }
    return bytes;
}

// --- Cryptography Functions ---

async function getCryptoKey(secret: string, usages: KeyUsage[]): Promise<CryptoKey> {
    const keyMaterial = encoder.encode(secret);
    const keyDigest = await crypto.subtle.digest(KEY_DIGEST_ALGORITHM, keyMaterial);

    return crypto.subtle.importKey('raw', keyDigest, { name: ALGORITHM }, false, usages);
}

/**
 * Encrypts plaintext using SubtleCrypto (AES-GCM).
 * @param text The plaintext to encrypt.
 * @param secret The secret key for encryption.
 * @returns A promise that resolves to the encrypted data, formatted as 'iv:encryptedText' in base64.
 */
export async function encrypt(text: string, secret: string = process.env.TOKEN_SECRET!): Promise<string> {
    const key = await getCryptoKey(secret, ['encrypt']);
    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));
    const plaintextBytes = encoder.encode(text);

    const encryptedBuffer = await crypto.subtle.encrypt(
        { name: ALGORITHM, iv: iv },
        key,
        plaintextBytes
    );

    const ivBase64 = arrayBufferToBase64(iv.buffer);
    const encryptedBase64 = arrayBufferToBase64(encryptedBuffer);

    return `${ivBase64}:${encryptedBase64}`;
}

/**
 * Decrypts ciphertext using SubtleCrypto (AES-GCM).
 * @param encryptedData The encrypted data string in 'iv:encryptedText' (base64) format.
 * @param secret The secret key for decryption.
 * @returns A promise that resolves to the decrypted plaintext, or null if authentication fails.
 */
export async function decrypt(encryptedData: string, secret: string = process.env.TOKEN_SECRET!): Promise<string | null> {
    try {
        const key = await getCryptoKey(secret, ['decrypt']);
        const parts = encryptedData.split(':');

        if (parts.length !== 2) {
            throw new Error('Invalid encrypted data format.');
        }

        const iv = base64ToUint8Array(parts[0]);
        const encryptedBytes = base64ToUint8Array(parts[1]);

        const decryptedBuffer = await crypto.subtle.decrypt(
            { name: ALGORITHM, iv: iv },
            key,
            encryptedBytes
        );

        return decoder.decode(decryptedBuffer);
    } catch (error) {
        if (error instanceof Error) {
            console.error('Decryption failed:', error.message);
        } else {
            console.error('An unknown decryption error occurred:', error);
        }
        return null;
    }
}