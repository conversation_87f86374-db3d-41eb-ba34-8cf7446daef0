import {internalQuery} from "./_generated/server";
import {v} from "convex/values";
import {decrypt} from "./crypt";
import {api} from "./_generated/api";


export const getPlexToken = internalQuery({
    args: {},
    returns: v.string(),
    handler: async (ctx) => {
        const currentUser = await ctx.runQuery(api.auth.currentUser, {});

        if (!currentUser) {
            throw new Error("No user found");
        }

        const token = currentUser.token;
        const decrypted = await decrypt(token);
        if (!decrypted) {
            throw new Error("Failed to decrypt token");
        }
        return decrypted;
    },
})