import {mutation, query} from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import {getAuthUserId} from "@convex-dev/auth/server";

// Manually sync Plex library
export const syncPlexLibrary = mutation({
  args: {},
  returns: v.string(),
  handler: async (ctx) => {
    try {
      // Schedule the sync action
      await ctx.scheduler.runAfter(0, internal.plex.syncPlexLibrary, {});
      return "Plex library sync has been scheduled.";
    } catch (error) {
      console.error("Error scheduling Plex sync:", error);
      return "Error scheduling Plex library sync.";
    }
  },
});

// Manually generate recommendations for a user
export const generateRecommendations = mutation({
  args: {},
  returns: v.string(),
  handler: async (ctx, args) => {
    try {
      const userId = await getAuthUserId(ctx);
      if (!userId) {
        return "No user ID available";
      }

      // Schedule the recommendation generation
      await ctx.scheduler.runAfter(0, internal.tmdb.generateRecommendations, {
        userId,
      });
      return "Recommendation generation has been scheduled.";
    } catch (error) {
      console.error("Error scheduling recommendation generation:", error);
      return "Error scheduling recommendation generation.";
    }
  },
});

