import {convexAuth, getAuthUserId} from "@convex-dev/auth/server";
import Plex from "./CustomProfile";
import {query} from "./_generated/server";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
    providers: [Plex],
});

export const currentUser = query({
    args: {},
    handler: async (ctx) => {
        const userId = await getAuthUserId(ctx);
        if (userId === null) {
            return null;
        }
        return await ctx.db.get(userId);
    },
});